import 'package:flutter/material.dart';
import '../models/puzzle_game.dart';
import '../models/puzzle_piece.dart';
import 'puzzle_piece_widget.dart';

class PuzzleBoard extends StatelessWidget {
  final PuzzleGame game;
  final double boardSize;
  final Function(PuzzlePiece, int, int) onPiecePlaced;
  
  const PuzzleBoard({
    super.key,
    required this.game,
    required this.boardSize,
    required this.onPiecePlaced,
  });
  
  @override
  Widget build(BuildContext context) {
    final pieceSize = boardSize / game.rows;
    
    return Container(
      width: boardSize,
      height: boardSize,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 2),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade50,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: game.cols,
            childAspectRatio: 1.0,
          ),
          itemCount: game.rows * game.cols,
          itemBuilder: (context, index) {
            final row = index ~/ game.cols;
            final col = index % game.cols;
            final piece = game.board[row][col];
            
            return DragTarget<PuzzlePiece>(
              onAcceptWithDetails: (details) {
                onPiecePlaced(details.data, row, col);
              },
              onWillAcceptWithDetails: (details) => true,
              builder: (context, candidateData, rejectedData) {
                final isHighlighted = candidateData.isNotEmpty;
                
                return Container(
                  margin: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isHighlighted 
                          ? Colors.blue.shade400 
                          : Colors.grey.shade300,
                      width: isHighlighted ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    color: isHighlighted 
                        ? Colors.blue.shade50 
                        : piece != null 
                            ? Colors.transparent 
                            : Colors.white,
                  ),
                  child: piece != null
                      ? GestureDetector(
                          onTap: () {
                            // Remove piece from board when tapped
                            game.removePieceFromBoard(piece);
                          },
                          child: PuzzlePieceWidget(
                            piece: piece,
                            size: pieceSize - 2,
                            showBorder: false,
                          ),
                        )
                      : Center(
                          child: Text(
                            '${row + 1},${col + 1}',
                            style: TextStyle(
                              color: Colors.grey.shade400,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
