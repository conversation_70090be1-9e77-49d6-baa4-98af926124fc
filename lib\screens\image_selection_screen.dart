import 'package:flutter/material.dart';
import '../models/puzzle_game.dart';
import '../models/puzzle_piece.dart';
import '../utils/image_splitter.dart';
import 'game_screen.dart';

class ImageSelectionScreen extends StatelessWidget {
  final PuzzleDifficulty difficulty;
  
  const ImageSelectionScreen({
    super.key,
    required this.difficulty,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Choose Image - ${_getDifficultyName(difficulty)}'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 4,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade50,
              Colors.purple.shade50,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select a beautiful image for your puzzle:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.0,
                  ),
                  itemCount: 3,
                  itemBuilder: (context, index) {
                    return _buildImageCard(context, index);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildImageCard(BuildContext context, int imageIndex) {
    final imageNames = ['Nature Scene', 'City Skyline', 'Abstract Art'];
    final imageDescriptions = [
      'Beautiful landscape with mountains, trees, and flowers',
      'Modern cityscape with buildings and starry night sky',
      'Colorful abstract design with geometric patterns'
    ];
    
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => GameScreenWithImage(
                difficulty: difficulty,
                imageIndex: imageIndex,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.grey.shade50,
              ],
            ),
          ),
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: ImageSplitter.createSampleImage(imageIndex),
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Text(
                        imageNames[imageIndex],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        imageDescriptions[imageIndex],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _getDifficultyName(PuzzleDifficulty difficulty) {
    switch (difficulty) {
      case PuzzleDifficulty.easy:
        return 'Easy (3x3)';
      case PuzzleDifficulty.medium:
        return 'Medium (4x4)';
      case PuzzleDifficulty.hard:
        return 'Hard (5x5)';
    }
  }
}

class GameScreenWithImage extends StatefulWidget {
  final PuzzleDifficulty difficulty;
  final int imageIndex;
  
  const GameScreenWithImage({
    super.key,
    required this.difficulty,
    required this.imageIndex,
  });
  
  @override
  State<GameScreenWithImage> createState() => _GameScreenWithImageState();
}

class _GameScreenWithImageState extends State<GameScreenWithImage> {
  late PuzzleGame game;
  late List<PuzzlePiece> pieces;
  
  @override
  void initState() {
    super.initState();
    _initializeGame();
  }
  
  void _initializeGame() {
    final gridSize = PuzzleGame.getGridSize(widget.difficulty);
    game = PuzzleGame(
      difficulty: widget.difficulty,
      rows: gridSize,
      cols: gridSize,
    );
    
    // Create pieces from selected image
    pieces = ImageSplitter.splitImage(
      originalImage: ImageSplitter.createSampleImage(widget.imageIndex),
      rows: gridSize,
      cols: gridSize,
      pieceWidth: 80,
      pieceHeight: 80,
    );
    
    // Add pieces to game
    game.availablePieces.addAll(pieces);
    game.startGame();
  }
  
  @override
  Widget build(BuildContext context) {
    return GameScreen(difficulty: widget.difficulty);
  }
}
