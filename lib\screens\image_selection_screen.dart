import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/puzzle_game.dart';
import '../utils/image_processor.dart';
import 'realistic_puzzle_game_screen.dart';

class ImageSelectionScreen extends StatefulWidget {
  final PuzzleDifficulty difficulty;

  const ImageSelectionScreen({
    super.key,
    required this.difficulty,
  });

  @override
  State<ImageSelectionScreen> createState() => _ImageSelectionScreenState();
}

class _ImageSelectionScreenState extends State<ImageSelectionScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Choose Image - ${_getDifficultyName(widget.difficulty)}'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 4,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade50,
              Colors.purple.shade50,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select a beautiful image for your puzzle:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 20),

              // Upload custom image button
              Card(
                elevation: 4,
                child: InkWell(
                  onTap: _isLoading ? null : () => _pickCustomImage(),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    height: 80,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        colors: [Colors.purple.shade400, Colors.blue.shade400],
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.add_photo_alternate,
                          size: 40,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Upload Your Own Image',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                'Choose from gallery or camera',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (_isLoading)
                          CircularProgressIndicator(color: Colors.white)
                        else
                          Icon(Icons.arrow_forward_ios, color: Colors.white),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              const Text(
                'Or choose from our collection:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.0,
                  ),
                  itemCount: 3,
                  itemBuilder: (context, index) {
                    return _buildImageCard(context, index);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickCustomImage() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Show options for camera or gallery
      final ImageSource? source = await showDialog<ImageSource>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Select Image Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () => Navigator.pop(context, ImageSource.camera),
              ),
            ],
          ),
        ),
      );

      if (source != null) {
        final XFile? imageFile = await ImageProcessor.pickImage(source: source);
        if (imageFile != null) {
          final ui.Image? image = await ImageProcessor.xFileToUiImage(imageFile);
          if (image != null) {
            if (ImageProcessor.isImageSuitableForPuzzle(image)) {
              // Process image for puzzle
              final Size puzzleSize = Size(400, 400);
              final processedImage = await ImageProcessor.processImageForPuzzle(
                sourceImage: image,
                targetSize: puzzleSize,
              );

              // Navigate to game screen
              if (mounted) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => RealisticPuzzleGameScreen(
                      sourceImage: processedImage,
                      difficulty: widget.difficulty,
                      puzzleSize: puzzleSize,
                    ),
                  ),
                );
              }
            } else {
              _showErrorDialog('Image not suitable for puzzle. Please choose a larger image with better aspect ratio.');
            }
          } else {
            _showErrorDialog('Failed to process the selected image.');
          }
        }
      }
    } catch (e) {
      _showErrorDialog('Error loading image: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildImageCard(BuildContext context, int imageIndex) {
    final imageNames = ['Nature Scene', 'City Skyline', 'Abstract Art'];
    final imageDescriptions = [
      'Beautiful landscape with mountains, trees, and flowers',
      'Modern cityscape with buildings and starry night sky',
      'Colorful abstract design with geometric patterns'
    ];
    
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FutureBuilder<ui.Image>(
                future: ImageProcessor.createSampleImage(Size(400, 400), imageIndex),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return RealisticPuzzleGameScreen(
                      sourceImage: snapshot.data!,
                      difficulty: widget.difficulty,
                      puzzleSize: Size(400, 400),
                    );
                  }
                  return Scaffold(
                    appBar: AppBar(title: Text('Loading...')),
                    body: Center(child: CircularProgressIndicator()),
                  );
                },
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.grey.shade50,
              ],
            ),
          ),
          child: Column(
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: FutureBuilder<ui.Image>(
                      future: ImageProcessor.createSampleImage(Size(200, 200), imageIndex),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          return RawImage(image: snapshot.data);
                        }
                        return Container(
                          color: Colors.grey.shade200,
                          child: Center(child: CircularProgressIndicator()),
                        );
                      },
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Text(
                        imageNames[imageIndex],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        imageDescriptions[imageIndex],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _getDifficultyName(PuzzleDifficulty difficulty) {
    switch (difficulty) {
      case PuzzleDifficulty.easy:
        return 'Easy (3x3)';
      case PuzzleDifficulty.medium:
        return 'Medium (4x4)';
      case PuzzleDifficulty.hard:
        return 'Hard (5x5)';
    }
  }
}


