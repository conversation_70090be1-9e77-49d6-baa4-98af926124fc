import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/puzzle_game.dart';
import '../models/puzzle_piece.dart';
import '../utils/image_splitter.dart';
import '../utils/game_utils.dart';
import '../widgets/puzzle_board.dart';
import '../widgets/puzzle_piece_widget.dart';

class GameScreen extends StatefulWidget {
  final PuzzleDifficulty difficulty;
  
  const GameScreen({
    super.key,
    required this.difficulty,
  });
  
  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late PuzzleGame game;
  late List<PuzzlePiece> pieces;
  
  @override
  void initState() {
    super.initState();
    _initializeGame();
  }
  
  void _initializeGame() {
    final gridSize = PuzzleGame.getGridSize(widget.difficulty);
    game = PuzzleGame(
      difficulty: widget.difficulty,
      rows: gridSize,
      cols: gridSize,
    );
    
    // Create pieces from sample image
    pieces = ImageSplitter.splitImage(
      originalImage: ImageSplitter.createSampleImage(0),
      rows: gridSize,
      cols: gridSize,
      pieceWidth: 80,
      pieceHeight: 80,
    );
    
    // Add pieces to game
    game.availablePieces.addAll(pieces);
    game.startGame();
  }
  
  void _onPiecePlaced(PuzzlePiece piece, int row, int col) {
    setState(() {
      game.placePiece(piece, row, col);
    });
    
    if (game.isCompleted) {
      _showCompletionDialog();
    }
  }
  
  void _showCompletionDialog() {
    final score = GameUtils.calculateScore(
      gameDuration: game.gameDuration!,
      moves: game.moves,
      difficulty: widget.difficulty,
      usedHints: game.usedHints,
    );
    final rating = GameUtils.getPerformanceRating(score, widget.difficulty);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          '🎉 Congratulations!',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 24),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'You have successfully completed the puzzle!',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Text(
                    'Score: $score',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  Text(
                    rating,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Time: ${GameUtils.formatTime(game.gameDuration!)}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Moves: ${game.moves}',
              style: const TextStyle(fontSize: 16),
            ),
            if (game.usedHints)
              Text(
                'Hints used: ${game.hintsUsed}',
                style: const TextStyle(fontSize: 16),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetGame();
            },
            child: const Text('Play Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Back to Menu'),
          ),
        ],
      ),
    );
  }
  
  void _resetGame() {
    setState(() {
      game.resetGame();
      pieces.shuffle();
      game.availablePieces.clear();
      game.availablePieces.addAll(pieces);
      game.startGame();
    });
  }
  
  String _formatDuration(Duration duration) {
    return GameUtils.formatTime(duration);
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: game,
      child: Scaffold(
        appBar: AppBar(
          title: Text('Puzzle ${_getDifficultyName(widget.difficulty)}'),
          backgroundColor: Colors.blue.shade600,
          foregroundColor: Colors.white,
          elevation: 4,
          actions: [
            Consumer<PuzzleGame>(
              builder: (context, game, child) {
                return IconButton(
                  onPressed: game.remainingHints > 0 ? () => game.useHint() : null,
                  icon: Stack(
                    children: [
                      const Icon(Icons.lightbulb),
                      if (game.remainingHints > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              '${game.remainingHints}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  tooltip: 'Hint (${game.remainingHints} remaining)',
                );
              },
            ),
            IconButton(
              onPressed: _resetGame,
              icon: const Icon(Icons.refresh),
              tooltip: 'Reset Game',
            ),
          ],
        ),
        body: Consumer<PuzzleGame>(
          builder: (context, game, child) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Game stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatCard(
                        'Time',
                        game.gameDuration != null
                            ? _formatDuration(game.gameDuration!)
                            : '00:00',
                        Icons.timer,
                      ),
                      _buildStatCard(
                        'Moves',
                        '${game.moves}',
                        Icons.touch_app,
                      ),
                      _buildStatCard(
                        'Remaining',
                        '${game.availablePieces.length}',
                        Icons.extension,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // Game area
                  Expanded(
                    child: Row(
                      children: [
                        // Puzzle board
                        Expanded(
                          flex: 2,
                          child: Center(
                            child: PuzzleBoard(
                              game: game,
                              boardSize: 300,
                              onPiecePlaced: _onPiecePlaced,
                            ),
                          ),
                        ),
                        
                        const SizedBox(width: 16),
                        
                        // Available pieces
                        Expanded(
                          flex: 1,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                              color: Colors.grey.shade50,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Available Pieces',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Expanded(
                                  child: GridView.builder(
                                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      crossAxisSpacing: 8,
                                      mainAxisSpacing: 8,
                                    ),
                                    itemCount: game.availablePieces.length,
                                    itemBuilder: (context, index) {
                                      final piece = game.availablePieces[index];
                                      return DraggablePuzzlePiece(
                                        piece: piece,
                                        size: 60,
                                        isHinted: game.isPieceHinted(piece.id),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
  
  Widget _buildStatCard(String label, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.blue.shade600),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  String _getDifficultyName(PuzzleDifficulty difficulty) {
    switch (difficulty) {
      case PuzzleDifficulty.easy:
        return 'Easy (3x3)';
      case PuzzleDifficulty.medium:
        return 'Medium (4x4)';
      case PuzzleDifficulty.hard:
        return 'Hard (5x5)';
    }
  }
}
