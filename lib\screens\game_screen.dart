import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/puzzle_game.dart';
import '../models/puzzle_piece.dart';
import '../utils/image_splitter.dart';
import '../widgets/puzzle_board.dart';
import '../widgets/puzzle_piece_widget.dart';

class GameScreen extends StatefulWidget {
  final PuzzleDifficulty difficulty;
  
  const GameScreen({
    super.key,
    required this.difficulty,
  });
  
  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late PuzzleGame game;
  late List<PuzzlePiece> pieces;
  
  @override
  void initState() {
    super.initState();
    _initializeGame();
  }
  
  void _initializeGame() {
    final gridSize = PuzzleGame.getGridSize(widget.difficulty);
    game = PuzzleGame(
      difficulty: widget.difficulty,
      rows: gridSize,
      cols: gridSize,
    );
    
    // Create pieces from sample image
    pieces = ImageSplitter.splitImage(
      originalImage: ImageSplitter.createSampleImage(),
      rows: gridSize,
      cols: gridSize,
      pieceWidth: 80,
      pieceHeight: 80,
    );
    
    // Add pieces to game
    game.availablePieces.addAll(pieces);
    game.startGame();
  }
  
  void _onPiecePlaced(PuzzlePiece piece, int row, int col) {
    setState(() {
      game.placePiece(piece, row, col);
    });
    
    if (game.isCompleted) {
      _showCompletionDialog();
    }
  }
  
  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          '🎉 تهانينا!',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 24),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'لقد أكملت البازل بنجاح!',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            Text(
              'الوقت: ${_formatDuration(game.gameDuration!)}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'عدد الحركات: ${game.moves}',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetGame();
            },
            child: const Text('لعب مرة أخرى'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('العودة للقائمة'),
          ),
        ],
      ),
    );
  }
  
  void _resetGame() {
    setState(() {
      game.resetGame();
      pieces.shuffle();
      game.availablePieces.clear();
      game.availablePieces.addAll(pieces);
      game.startGame();
    });
  }
  
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: game,
      child: Scaffold(
        appBar: AppBar(
          title: Text('بازل ${_getDifficultyName(widget.difficulty)}'),
          backgroundColor: Colors.blue.shade600,
          foregroundColor: Colors.white,
          actions: [
            IconButton(
              onPressed: _resetGame,
              icon: const Icon(Icons.refresh),
              tooltip: 'إعادة تشغيل',
            ),
          ],
        ),
        body: Consumer<PuzzleGame>(
          builder: (context, game, child) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Game stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatCard(
                        'الوقت',
                        game.gameDuration != null 
                            ? _formatDuration(game.gameDuration!)
                            : '00:00',
                        Icons.timer,
                      ),
                      _buildStatCard(
                        'الحركات',
                        '${game.moves}',
                        Icons.touch_app,
                      ),
                      _buildStatCard(
                        'المتبقي',
                        '${game.availablePieces.length}',
                        Icons.extension,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // Game area
                  Expanded(
                    child: Row(
                      children: [
                        // Puzzle board
                        Expanded(
                          flex: 2,
                          child: Center(
                            child: PuzzleBoard(
                              game: game,
                              boardSize: 300,
                              onPiecePlaced: _onPiecePlaced,
                            ),
                          ),
                        ),
                        
                        const SizedBox(width: 16),
                        
                        // Available pieces
                        Expanded(
                          flex: 1,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                              color: Colors.grey.shade50,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'القطع المتاحة',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Expanded(
                                  child: GridView.builder(
                                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      crossAxisSpacing: 8,
                                      mainAxisSpacing: 8,
                                    ),
                                    itemCount: game.availablePieces.length,
                                    itemBuilder: (context, index) {
                                      final piece = game.availablePieces[index];
                                      return DraggablePuzzlePiece(
                                        piece: piece,
                                        size: 60,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
  
  Widget _buildStatCard(String label, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.blue.shade600),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  String _getDifficultyName(PuzzleDifficulty difficulty) {
    switch (difficulty) {
      case PuzzleDifficulty.easy:
        return 'سهل (3x3)';
      case PuzzleDifficulty.medium:
        return 'متوسط (4x4)';
      case PuzzleDifficulty.hard:
        return 'صعب (5x5)';
    }
  }
}
