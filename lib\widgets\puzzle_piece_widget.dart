import 'package:flutter/material.dart';
import '../models/puzzle_piece.dart';

class PuzzlePieceWidget extends StatelessWidget {
  final PuzzlePiece piece;
  final double size;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool showBorder;
  
  const PuzzlePieceWidget({
    super.key,
    required this.piece,
    required this.size,
    this.onTap,
    this.isSelected = false,
    this.showBorder = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          border: showBorder ? Border.all(
            color: isSelected 
                ? Colors.blue.shade600 
                : piece.isInCorrectPosition 
                    ? Colors.green.shade600
                    : Colors.grey.shade400,
            width: isSelected ? 3 : 2,
          ) : null,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                blurRadius: 8,
                spreadRadius: 2,
              ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(6),
          child: piece.imageWidget,
        ),
      ),
    );
  }
}

class DraggablePuzzlePiece extends StatelessWidget {
  final PuzzlePiece piece;
  final double size;
  final bool isSelected;
  
  const DraggablePuzzlePiece({
    super.key,
    required this.piece,
    required this.size,
    this.isSelected = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Draggable<PuzzlePiece>(
      data: piece,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: PuzzlePieceWidget(
          piece: piece,
          size: size,
          isSelected: true,
          showBorder: true,
        ),
      ),
      childWhenDragging: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300, width: 2),
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.shade100,
        ),
        child: Icon(
          Icons.drag_indicator,
          color: Colors.grey.shade400,
          size: size * 0.3,
        ),
      ),
      child: PuzzlePieceWidget(
        piece: piece,
        size: size,
        isSelected: isSelected,
      ),
    );
  }
}
