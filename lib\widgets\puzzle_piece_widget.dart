import 'package:flutter/material.dart';
import '../models/puzzle_piece.dart';
import '../utils/puzzle_piece_painter.dart';

class PuzzlePieceWidget extends StatelessWidget {
  final PuzzlePiece piece;
  final double size;
  final VoidCallback? onTap;
  final bool isSelected;
  final bool showBorder;
  
  const PuzzlePieceWidget({
    super.key,
    required this.piece,
    required this.size,
    this.onTap,
    this.isSelected = false,
    this.showBorder = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        child: Stack(
          children: [
            // Clipped image
            ClipPath(
              clipper: PuzzlePieceClipper(
                hasTopTab: piece.hasTopTab,
                hasRightTab: piece.hasRightTab,
                hasBottomTab: piece.hasBottomTab,
                hasLeftTab: piece.hasLeftTab,
              ),
              child: piece.imageWidget,
            ),
            // Custom painted border and effects
            CustomPaint(
              size: Size(size, size),
              painter: Puzzle<PERSON>iecePainter(
                hasTopTab: piece.hasTopTab,
                hasRightTab: piece.hasRightTab,
                hasBottomTab: piece.hasBottomTab,
                hasLeftTab: piece.hasLeftTab,
                isSelected: isSelected,
                isCorrectPosition: piece.isInCorrectPosition,
                borderWidth: showBorder ? (isSelected ? 3 : 2) : 0,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DraggablePuzzlePiece extends StatelessWidget {
  final PuzzlePiece piece;
  final double size;
  final bool isSelected;
  
  const DraggablePuzzlePiece({
    super.key,
    required this.piece,
    required this.size,
    this.isSelected = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Draggable<PuzzlePiece>(
      data: piece,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: PuzzlePieceWidget(
          piece: piece,
          size: size,
          isSelected: true,
          showBorder: true,
        ),
      ),
      childWhenDragging: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300, width: 2),
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey.shade100,
        ),
        child: Icon(
          Icons.drag_indicator,
          color: Colors.grey.shade400,
          size: size * 0.3,
        ),
      ),
      child: PuzzlePieceWidget(
        piece: piece,
        size: size,
        isSelected: isSelected,
      ),
    );
  }
}
