import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/puzzle_game.dart';
import '../models/puzzle_piece.dart';

class GameUtils {
  static int calculateScore({
    required Duration gameDuration,
    required int moves,
    required PuzzleDifficulty difficulty,
    required bool usedHints,
  }) {
    // Base score based on difficulty
    int baseScore = switch (difficulty) {
      PuzzleDifficulty.easy => 1000,
      PuzzleDifficulty.medium => 2000,
      PuzzleDifficulty.hard => 3000,
    };
    
    // Time bonus (faster completion = higher score)
    int timeBonus = math.max(0, baseScore - (gameDuration.inSeconds * 10));
    
    // Move penalty (fewer moves = higher score)
    int movePenalty = moves * 5;
    
    // Hint penalty
    int hintPenalty = usedHints ? baseScore ~/ 4 : 0;
    
    int finalScore = baseScore + timeBonus - movePenalty - hintPenalty;
    return math.max(100, finalScore); // Minimum score of 100
  }
  
  static String getPerformanceRating(int score, PuzzleDifficulty difficulty) {
    int threshold = switch (difficulty) {
      PuzzleDifficulty.easy => 1500,
      PuzzleDifficulty.medium => 2500,
      PuzzleDifficulty.hard => 3500,
    };
    
    if (score >= threshold * 1.5) return "🏆 Master";
    if (score >= threshold * 1.2) return "⭐ Expert";
    if (score >= threshold) return "👍 Good";
    if (score >= threshold * 0.7) return "👌 Fair";
    return "💪 Keep Trying";
  }
  
  static List<PuzzlePiece> getHintPieces(PuzzleGame game) {
    // Find pieces that are in correct positions but not placed
    List<PuzzlePiece> hintPieces = [];
    
    for (var piece in game.availablePieces) {
      // Check if this piece belongs to an empty spot on the board
      if (game.board[piece.correctRow][piece.correctCol] == null) {
        hintPieces.add(piece);
        if (hintPieces.length >= 3) break; // Limit to 3 hints
      }
    }
    
    return hintPieces;
  }
  
  static bool isPieceNearCorrectPosition(PuzzlePiece piece, int boardRow, int boardCol) {
    // Check if the piece is within 1 position of its correct location
    int rowDiff = (piece.correctRow - boardRow).abs();
    int colDiff = (piece.correctCol - boardCol).abs();
    
    return rowDiff <= 1 && colDiff <= 1;
  }
  
  static String formatTime(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    
    if (duration.inHours > 0) {
      return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
    } else {
      return "$twoDigitMinutes:$twoDigitSeconds";
    }
  }
  
  static Color getDifficultyColor(PuzzleDifficulty difficulty) {
    return switch (difficulty) {
      PuzzleDifficulty.easy => const Color(0xFF4CAF50),
      PuzzleDifficulty.medium => const Color(0xFFFF9800),
      PuzzleDifficulty.hard => const Color(0xFFF44336),
    };
  }
  
  static String getDifficultyDescription(PuzzleDifficulty difficulty) {
    return switch (difficulty) {
      PuzzleDifficulty.easy => "Perfect for beginners\n9 pieces to arrange",
      PuzzleDifficulty.medium => "A fun challenge\n16 pieces to arrange",
      PuzzleDifficulty.hard => "For puzzle masters\n25 pieces to arrange",
    };
  }
}
