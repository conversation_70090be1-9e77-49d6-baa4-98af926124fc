import 'dart:math' as math;
import 'package:flutter/material.dart';

class PuzzlePiecePainter extends CustomPainter {
  final bool hasTopTab;
  final bool hasRightTab;
  final bool hasBottomTab;
  final bool hasLeftTab;
  final Color borderColor;
  final double borderWidth;
  final bool isSelected;
  final bool isCorrectPosition;

  PuzzlePiecePainter({
    required this.hasTopTab,
    required this.hasRightTab,
    required this.hasBottomTab,
    required this.hasLeftTab,
    this.borderColor = Colors.black,
    this.borderWidth = 2.0,
    this.isSelected = false,
    this.isCorrectPosition = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isSelected 
          ? Colors.blue.withValues(alpha: 0.3)
          : isCorrectPosition 
              ? Colors.green.withValues(alpha: 0.2)
              : Colors.transparent
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = isSelected 
          ? Colors.blue.shade600
          : isCorrectPosition 
              ? Colors.green.shade600
              : borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = isSelected ? borderWidth + 1 : borderWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

    final path = _createPuzzlePiecePath(size);
    
    // Draw shadow
    canvas.save();
    canvas.translate(2, 2);
    canvas.drawPath(path, shadowPaint);
    canvas.restore();
    
    // Draw fill
    canvas.drawPath(path, paint);
    
    // Draw border
    canvas.drawPath(path, borderPaint);
  }

  Path _createPuzzlePiecePath(Size size) {
    final path = Path();
    final tabSize = size.width * 0.2;
    final curveSize = tabSize * 0.6;
    
    // Start from top-left corner
    path.moveTo(0, hasLeftTab ? tabSize : 0);
    
    // Left side
    if (hasLeftTab) {
      _addTab(path, 0, tabSize, tabSize, 0, TabDirection.left);
    }
    path.lineTo(0, size.height - (hasLeftTab ? tabSize : 0));
    
    // Bottom side
    path.lineTo(hasBottomTab ? size.width / 2 - tabSize / 2 : 0, size.height);
    if (hasBottomTab) {
      _addTab(path, size.width / 2 - tabSize / 2, size.height, 
              size.width / 2 + tabSize / 2, size.height, TabDirection.bottom);
    }
    path.lineTo(size.width, size.height);
    
    // Right side
    path.lineTo(size.width, hasRightTab ? size.height - tabSize : size.height);
    if (hasRightTab) {
      _addTab(path, size.width, size.height - tabSize, 
              size.width, size.height / 2, TabDirection.right);
    }
    path.lineTo(size.width, hasRightTab ? tabSize : 0);
    
    // Top side
    path.lineTo(hasTopTab ? size.width / 2 + tabSize / 2 : size.width, 0);
    if (hasTopTab) {
      _addTab(path, size.width / 2 + tabSize / 2, 0, 
              size.width / 2 - tabSize / 2, 0, TabDirection.top);
    }
    path.lineTo(hasLeftTab ? tabSize : 0, 0);
    
    path.close();
    return path;
  }

  void _addTab(Path path, double startX, double startY, double endX, double endY, TabDirection direction) {
    final tabSize = math.min((endX - startX).abs(), (endY - startY).abs());
    final controlOffset = tabSize * 0.4;
    
    switch (direction) {
      case TabDirection.top:
        path.quadraticBezierTo(
          startX - controlOffset, startY - tabSize,
          (startX + endX) / 2, startY - tabSize
        );
        path.quadraticBezierTo(
          endX + controlOffset, startY - tabSize,
          endX, endY
        );
        break;
      case TabDirection.right:
        path.quadraticBezierTo(
          startX + tabSize, startY - controlOffset,
          startX + tabSize, (startY + endY) / 2
        );
        path.quadraticBezierTo(
          startX + tabSize, endY + controlOffset,
          endX, endY
        );
        break;
      case TabDirection.bottom:
        path.quadraticBezierTo(
          startX + controlOffset, startY + tabSize,
          (startX + endX) / 2, startY + tabSize
        );
        path.quadraticBezierTo(
          endX - controlOffset, startY + tabSize,
          endX, endY
        );
        break;
      case TabDirection.left:
        path.quadraticBezierTo(
          startX - tabSize, startY + controlOffset,
          startX - tabSize, (startY + endY) / 2
        );
        path.quadraticBezierTo(
          startX - tabSize, endY - controlOffset,
          endX, endY
        );
        break;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! PuzzlePiecePainter ||
        oldDelegate.hasTopTab != hasTopTab ||
        oldDelegate.hasRightTab != hasRightTab ||
        oldDelegate.hasBottomTab != hasBottomTab ||
        oldDelegate.hasLeftTab != hasLeftTab ||
        oldDelegate.isSelected != isSelected ||
        oldDelegate.isCorrectPosition != isCorrectPosition;
  }
}

enum TabDirection { top, right, bottom, left }

class PuzzlePieceClipper extends CustomClipper<Path> {
  final bool hasTopTab;
  final bool hasRightTab;
  final bool hasBottomTab;
  final bool hasLeftTab;

  PuzzlePieceClipper({
    required this.hasTopTab,
    required this.hasRightTab,
    required this.hasBottomTab,
    required this.hasLeftTab,
  });

  @override
  Path getClip(Size size) {
    final painter = PuzzlePiecePainter(
      hasTopTab: hasTopTab,
      hasRightTab: hasRightTab,
      hasBottomTab: hasBottomTab,
      hasLeftTab: hasLeftTab,
    );
    return painter._createPuzzlePiecePath(size);
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return oldClipper is! PuzzlePieceClipper ||
        oldClipper.hasTopTab != hasTopTab ||
        oldClipper.hasRightTab != hasRightTab ||
        oldClipper.hasBottomTab != hasBottomTab ||
        oldClipper.hasLeftTab != hasLeftTab;
  }
}
