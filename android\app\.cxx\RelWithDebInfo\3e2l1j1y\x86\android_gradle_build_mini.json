{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\ArzaPuzzle\\arzapuzzle\\android\\app\\.cxx\\RelWithDebInfo\\3e2l1j1y\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\ArzaPuzzle\\arzapuzzle\\android\\app\\.cxx\\RelWithDebInfo\\3e2l1j1y\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}