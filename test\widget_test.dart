// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:arzapuzzle/main.dart';

void main() {
  testWidgets('Puzzle app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const PuzzleApp());

    // Verify that the home screen loads with the title
    expect(find.text('لعبة البازل'), findsOneWidget);

    // Verify that difficulty buttons are present
    expect(find.text('سهل (3×3)'), findsOneWidget);
    expect(find.text('متوسط (4×4)'), findsOneWidget);
    expect(find.text('صعب (5×5)'), findsOneWidget);
  });
}
