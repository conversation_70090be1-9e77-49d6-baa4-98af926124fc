import 'package:flutter/material.dart';

class PuzzlePiece {
  final int id;
  final int correctRow;
  final int correctCol;
  int currentRow;
  int currentCol;
  final Widget imageWidget;
  bool isPlaced;
  final bool hasTopTab;
  final bool hasRightTab;
  final bool hasBottomTab;
  final bool hasLeftTab;

  PuzzlePiece({
    required this.id,
    required this.correctRow,
    required this.correctCol,
    required this.currentRow,
    required this.currentCol,
    required this.imageWidget,
    required this.hasTopTab,
    required this.hasRightTab,
    required this.hasBottomTab,
    required this.hasLeftTab,
    this.isPlaced = false,
  });
  
  bool get isInCorrectPosition => 
      currentRow == correctRow && currentCol == correctCol;
  
  PuzzlePiece copyWith({
    int? currentRow,
    int? currentCol,
    bool? isPlaced,
  }) {
    return PuzzlePiece(
      id: id,
      correctRow: correctRow,
      correctCol: correctCol,
      currentRow: currentRow ?? this.currentRow,
      currentCol: currentCol ?? this.currentCol,
      imageWidget: imageWidget,
      hasTopTab: hasTopTab,
      hasRightTab: hasRightTab,
      hasBottomTab: hasBottomTab,
      hasLeftTab: hasLeftTab,
      isPlaced: isPlaced ?? this.isPlaced,
    );
  }
}
