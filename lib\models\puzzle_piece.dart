import 'package:flutter/material.dart';

class PuzzlePiece {
  final int id;
  final int correctRow;
  final int correctCol;
  int currentRow;
  int currentCol;
  final Widget imageWidget;
  bool isPlaced;
  
  PuzzlePiece({
    required this.id,
    required this.correctRow,
    required this.correctCol,
    required this.currentRow,
    required this.currentCol,
    required this.imageWidget,
    this.isPlaced = false,
  });
  
  bool get isInCorrectPosition => 
      currentRow == correctRow && currentCol == correctCol;
  
  PuzzlePiece copyWith({
    int? currentRow,
    int? currentCol,
    bool? isPlaced,
  }) {
    return PuzzlePiece(
      id: id,
      correctRow: correctRow,
      correctCol: correctCol,
      currentRow: currentRow ?? this.currentRow,
      currentCol: currentCol ?? this.currentCol,
      imageWidget: imageWidget,
      isPlaced: isPlaced ?? this.isPlaced,
    );
  }
}
