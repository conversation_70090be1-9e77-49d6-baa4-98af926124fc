import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/realistic_puzzle_piece_model.dart';
import '../models/puzzle_game.dart';
import '../utils/puzzle_snapping_system.dart';
import '../utils/game_utils.dart';
import '../widgets/realistic_puzzle_piece.dart';

class RealisticPuzzleGameScreen extends StatefulWidget {
  final ui.Image sourceImage;
  final PuzzleDifficulty difficulty;
  final Size puzzleSize;
  
  const RealisticPuzzleGameScreen({
    super.key,
    required this.sourceImage,
    required this.difficulty,
    required this.puzzleSize,
  });
  
  @override
  State<RealisticPuzzleGameScreen> createState() => _RealisticPuzzleGameScreenState();
}

class _RealisticPuzzleGameScreenState extends State<RealisticPuzzleGameScreen>
    with TickerProviderStateMixin {
  late RealisticPuzzleGameModel game;
  late Size pieceSize;
  RealisticPuzzlePieceModel? draggedPiece;
  Offset? dragOffset;
  
  @override
  void initState() {
    super.initState();
    _initializeGame();
  }
  
  void _initializeGame() {
    final gridSize = PuzzleGame.getGridSize(widget.difficulty);
    pieceSize = Size(
      widget.puzzleSize.width / gridSize,
      widget.puzzleSize.height / gridSize,
    );
    
    game = RealisticPuzzleGameModel(
      sourceImage: widget.sourceImage,
      rows: gridSize,
      cols: gridSize,
      pieceSize: pieceSize,
    );
    
    game.startGame();
  }
  
  void _onPieceDragStart(RealisticPuzzlePieceModel piece, Offset localPosition) {
    setState(() {
      draggedPiece = piece;
      dragOffset = localPosition;
      game.selectedPiece = piece;
    });
  }
  
  void _onPieceDragUpdate(Offset globalPosition) {
    if (draggedPiece != null && dragOffset != null) {
      setState(() {
        draggedPiece!.currentPosition = globalPosition - dragOffset!;
      });
    }
  }
  
  void _onPieceDragEnd() {
    if (draggedPiece != null) {
      final snapResult = PuzzleSnappingSystem.findBestSnapPosition(
        draggedPiece: draggedPiece!,
        dropPosition: draggedPiece!.currentPosition,
        board: game.board,
        pieceSize: pieceSize,
        gridRows: game.rows,
        gridCols: game.cols,
      );
      
      if (snapResult.canSnap) {
        // Animate to snap position
        PuzzleSnappingSystem.animateToSnapPosition(
          piece: draggedPiece!,
          targetPosition: snapResult.position,
          vsync: this,
          onComplete: () {
            game.placePiece(draggedPiece!, snapResult.gridRow, snapResult.gridCol);
            if (game.isCompleted) {
              _showCompletionDialog();
            }
          },
        );
      }
      
      setState(() {
        draggedPiece = null;
        dragOffset = null;
        game.selectedPiece = null;
      });
    }
  }
  
  void _showCompletionDialog() {
    final score = GameUtils.calculateScore(
      gameDuration: game.gameDuration!,
      moves: game.moves,
      difficulty: widget.difficulty,
      usedHints: game.usedHints,
    );
    final rating = GameUtils.getPerformanceRating(score, widget.difficulty);
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          '🎉 Congratulations!',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 24),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'You have successfully completed the realistic puzzle!',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Text(
                    'Score: $score',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  Text(
                    rating,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Time: ${GameUtils.formatTime(game.gameDuration!)}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Moves: ${game.moves}',
              style: const TextStyle(fontSize: 16),
            ),
            if (game.usedHints)
              Text(
                'Hints used: ${game.hintsUsed}',
                style: const TextStyle(fontSize: 16),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetGame();
            },
            child: const Text('Play Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Back to Menu'),
          ),
        ],
      ),
    );
  }
  
  void _resetGame() {
    setState(() {
      game.resetGame();
      draggedPiece = null;
      dragOffset = null;
    });
  }
  
  String _getDifficultyName(PuzzleDifficulty difficulty) {
    switch (difficulty) {
      case PuzzleDifficulty.easy:
        return 'Easy (3x3)';
      case PuzzleDifficulty.medium:
        return 'Medium (4x4)';
      case PuzzleDifficulty.hard:
        return 'Hard (5x5)';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: game,
      child: Scaffold(
        appBar: AppBar(
          title: Text('Realistic Puzzle ${_getDifficultyName(widget.difficulty)}'),
          backgroundColor: Colors.blue.shade600,
          foregroundColor: Colors.white,
          elevation: 4,
          actions: [
            Consumer<RealisticPuzzleGameModel>(
              builder: (context, game, child) {
                return IconButton(
                  onPressed: game.remainingHints > 0 ? () => game.useHint() : null,
                  icon: Stack(
                    children: [
                      const Icon(Icons.lightbulb),
                      if (game.remainingHints > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              '${game.remainingHints}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  tooltip: 'Hint (${game.remainingHints} remaining)',
                );
              },
            ),
            IconButton(
              onPressed: _resetGame,
              icon: const Icon(Icons.refresh),
              tooltip: 'Reset Game',
            ),
          ],
        ),
        body: Consumer<RealisticPuzzleGameModel>(
          builder: (context, game, child) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Game stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatCard(
                        'Time',
                        game.gameDuration != null 
                            ? GameUtils.formatTime(game.gameDuration!)
                            : '00:00',
                        Icons.timer,
                      ),
                      _buildStatCard(
                        'Moves',
                        '${game.moves}',
                        Icons.touch_app,
                      ),
                      _buildStatCard(
                        'Remaining',
                        '${game.availablePieces.length}',
                        Icons.extension,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  
                  // Game area
                  Expanded(
                    child: Row(
                      children: [
                        // Puzzle board
                        Expanded(
                          flex: 2,
                          child: Center(
                            child: _buildPuzzleBoard(),
                          ),
                        ),
                        
                        const SizedBox(width: 16),
                        
                        // Available pieces
                        Expanded(
                          flex: 1,
                          child: _buildAvailablePieces(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
  
  Widget _buildStatCard(String label, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.blue.shade600),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildPuzzleBoard() {
    return Container(
      width: widget.puzzleSize.width,
      height: widget.puzzleSize.height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 2),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade50,
      ),
      child: Stack(
        children: [
          // Board grid (for visual reference)
          CustomPaint(
            size: widget.puzzleSize,
            painter: BoardGridPainter(
              rows: game.rows,
              cols: game.cols,
              pieceSize: pieceSize,
            ),
          ),
          
          // Placed pieces
          ...game.board.expand((row) => row)
              .where((piece) => piece != null)
              .map((piece) => Positioned(
                left: piece!.currentPosition.dx,
                top: piece.currentPosition.dy,
                child: RealisticPuzzlePiece(
                  sourceImage: widget.sourceImage,
                  pieceSize: pieceSize,
                  imageOffset: piece.imageOffset,
                  tabConfig: piece.tabConfig,
                  row: piece.correctRow,
                  col: piece.correctCol,
                  isPlaced: piece.isPlaced,
                  isSelected: piece == game.selectedPiece,
                  isHinted: game.isPieceHinted(piece.id),
                  onTap: () => game.removePieceFromBoard(piece),
                ),
              )),
        ],
      ),
    );
  }
  
  Widget _buildAvailablePieces() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Pieces',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: game.availablePieces.length,
              itemBuilder: (context, index) {
                final piece = game.availablePieces[index];
                return GestureDetector(
                  onPanStart: (details) => _onPieceDragStart(piece, details.localPosition),
                  onPanUpdate: (details) => _onPieceDragUpdate(details.globalPosition),
                  onPanEnd: (details) => _onPieceDragEnd(),
                  child: RealisticPuzzlePiece(
                    sourceImage: widget.sourceImage,
                    pieceSize: Size(60, 60),
                    imageOffset: piece.imageOffset,
                    tabConfig: piece.tabConfig,
                    row: piece.correctRow,
                    col: piece.correctCol,
                    isSelected: piece == game.selectedPiece,
                    isHinted: game.isPieceHinted(piece.id),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class BoardGridPainter extends CustomPainter {
  final int rows;
  final int cols;
  final Size pieceSize;
  
  BoardGridPainter({
    required this.rows,
    required this.cols,
    required this.pieceSize,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade300
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;
    
    // Draw grid lines
    for (int i = 0; i <= cols; i++) {
      final x = i * pieceSize.width;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
    
    for (int i = 0; i <= rows; i++) {
      final y = i * pieceSize.height;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
