import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

class RealisticPuzzleGenerator {
  static const double tabRadius = 0.15; // Radius of the tab relative to piece size
  static const double tabDepth = 0.08; // How deep the tab goes into the piece
  static const double curveStrength = 0.4; // Strength of the bezier curves
  
  /// Generates a realistic puzzle piece path with proper tabs and blanks
  static Path generatePiecePath({
    required Size pieceSize,
    required bool hasTopTab,
    required bool hasRightTab,
    required bool hasBottomTab,
    required bool hasLeftTab,
    required bool topTabOut,
    required bool rightTabOut,
    required bool bottomTabOut,
    required bool leftTabOut,
    double randomSeed = 0,
  }) {
    final path = Path();
    final width = pieceSize.width;
    final height = pieceSize.height;
    
    // Add slight randomness to make each piece unique
    final random = math.Random(randomSeed.toInt());
    final tabRadiusVariation = tabRadius + (random.nextDouble() - 0.5) * 0.03;
    final tabDepthVariation = tabDepth + (random.nextDouble() - 0.5) * 0.02;
    
    // Start from top-left corner
    path.moveTo(0, 0);
    
    // Top edge with optional tab
    if (hasTopTab) {
      _addTopEdgeWithTab(path, width, tabRadiusVariation, tabDepthVariation, topTabOut);
    } else {
      path.lineTo(width, 0);
    }
    
    // Right edge with optional tab
    if (hasRightTab) {
      _addRightEdgeWithTab(path, width, height, tabRadiusVariation, tabDepthVariation, rightTabOut);
    } else {
      path.lineTo(width, height);
    }
    
    // Bottom edge with optional tab
    if (hasBottomTab) {
      _addBottomEdgeWithTab(path, width, height, tabRadiusVariation, tabDepthVariation, bottomTabOut);
    } else {
      path.lineTo(0, height);
    }
    
    // Left edge with optional tab
    if (hasLeftTab) {
      _addLeftEdgeWithTab(path, height, tabRadiusVariation, tabDepthVariation, leftTabOut);
    } else {
      path.lineTo(0, 0);
    }
    
    path.close();
    return path;
  }
  
  static void _addTopEdgeWithTab(Path path, double width, double tabRadius, double tabDepth, bool tabOut) {
    final tabCenter = width * 0.5;
    final tabWidth = width * tabRadius;
    final tabHeight = width * tabDepth * (tabOut ? -1 : 1);
    
    // Move to start of tab
    path.lineTo(tabCenter - tabWidth, 0);
    
    // Create smooth bezier curve for tab
    final cp1x = tabCenter - tabWidth * curveStrength;
    final cp1y = tabHeight * 0.3;
    final cp2x = tabCenter - tabWidth * 0.3;
    final cp2y = tabHeight * 0.8;
    
    path.cubicTo(cp1x, cp1y, cp2x, cp2y, tabCenter, tabHeight);
    
    // Complete the tab curve
    final cp3x = tabCenter + tabWidth * 0.3;
    final cp3y = tabHeight * 0.8;
    final cp4x = tabCenter + tabWidth * curveStrength;
    final cp4y = tabHeight * 0.3;
    
    path.cubicTo(cp3x, cp3y, cp4x, cp4y, tabCenter + tabWidth, 0);
    
    // Continue to end of edge
    path.lineTo(width, 0);
  }
  
  static void _addRightEdgeWithTab(Path path, double width, double height, double tabRadius, double tabDepth, bool tabOut) {
    final tabCenter = height * 0.5;
    final tabWidth = height * tabRadius;
    final tabHeight = height * tabDepth * (tabOut ? 1 : -1);
    
    // Move to start of tab
    path.lineTo(width, tabCenter - tabWidth);
    
    // Create smooth bezier curve for tab
    final cp1x = width + tabHeight * 0.3;
    final cp1y = tabCenter - tabWidth * curveStrength;
    final cp2x = width + tabHeight * 0.8;
    final cp2y = tabCenter - tabWidth * 0.3;
    
    path.cubicTo(cp1x, cp1y, cp2x, cp2y, width + tabHeight, tabCenter);
    
    // Complete the tab curve
    final cp3x = width + tabHeight * 0.8;
    final cp3y = tabCenter + tabWidth * 0.3;
    final cp4x = width + tabHeight * 0.3;
    final cp4y = tabCenter + tabWidth * curveStrength;
    
    path.cubicTo(cp3x, cp3y, cp4x, cp4y, width, tabCenter + tabWidth);
    
    // Continue to end of edge
    path.lineTo(width, height);
  }
  
  static void _addBottomEdgeWithTab(Path path, double width, double height, double tabRadius, double tabDepth, bool tabOut) {
    final tabCenter = width * 0.5;
    final tabWidth = width * tabRadius;
    final tabHeight = width * tabDepth * (tabOut ? 1 : -1);
    
    // Move to start of tab
    path.lineTo(tabCenter + tabWidth, height);
    
    // Create smooth bezier curve for tab
    final cp1x = tabCenter + tabWidth * curveStrength;
    final cp1y = height + tabHeight * 0.3;
    final cp2x = tabCenter + tabWidth * 0.3;
    final cp2y = height + tabHeight * 0.8;
    
    path.cubicTo(cp1x, cp1y, cp2x, cp2y, tabCenter, height + tabHeight);
    
    // Complete the tab curve
    final cp3x = tabCenter - tabWidth * 0.3;
    final cp3y = height + tabHeight * 0.8;
    final cp4x = tabCenter - tabWidth * curveStrength;
    final cp4y = height + tabHeight * 0.3;
    
    path.cubicTo(cp3x, cp3y, cp4x, cp4y, tabCenter - tabWidth, height);
    
    // Continue to end of edge
    path.lineTo(0, height);
  }
  
  static void _addLeftEdgeWithTab(Path path, double height, double tabRadius, double tabDepth, bool tabOut) {
    final tabCenter = height * 0.5;
    final tabWidth = height * tabRadius;
    final tabHeight = height * tabDepth * (tabOut ? -1 : 1);
    
    // Move to start of tab
    path.lineTo(0, tabCenter + tabWidth);
    
    // Create smooth bezier curve for tab
    final cp1x = tabHeight * 0.3;
    final cp1y = tabCenter + tabWidth * curveStrength;
    final cp2x = tabHeight * 0.8;
    final cp2y = tabCenter + tabWidth * 0.3;
    
    path.cubicTo(cp1x, cp1y, cp2x, cp2y, tabHeight, tabCenter);
    
    // Complete the tab curve
    final cp3x = tabHeight * 0.8;
    final cp3y = tabCenter - tabWidth * 0.3;
    final cp4x = tabHeight * 0.3;
    final cp4y = tabCenter - tabWidth * curveStrength;
    
    path.cubicTo(cp3x, cp3y, cp4x, cp4y, 0, tabCenter - tabWidth);
    
    // Continue to end of edge
    path.lineTo(0, 0);
  }
  
  /// Generates tab configuration for a puzzle grid ensuring matching tabs
  static List<List<PieceTabConfig>> generateTabConfiguration(int rows, int cols) {
    final random = math.Random();
    final config = List.generate(rows, (row) => 
        List.generate(cols, (col) => PieceTabConfig()));
    
    // Generate horizontal tabs
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols - 1; col++) {
        final tabOut = random.nextBool();
        config[row][col].hasRightTab = true;
        config[row][col].rightTabOut = tabOut;
        config[row][col + 1].hasLeftTab = true;
        config[row][col + 1].leftTabOut = !tabOut; // Opposite of the left piece
      }
    }
    
    // Generate vertical tabs
    for (int row = 0; row < rows - 1; row++) {
      for (int col = 0; col < cols; col++) {
        final tabOut = random.nextBool();
        config[row][col].hasBottomTab = true;
        config[row][col].bottomTabOut = tabOut;
        config[row + 1][col].hasTopTab = true;
        config[row + 1][col].topTabOut = !tabOut; // Opposite of the top piece
      }
    }
    
    return config;
  }
}

class PieceTabConfig {
  bool hasTopTab = false;
  bool hasRightTab = false;
  bool hasBottomTab = false;
  bool hasLeftTab = false;
  
  bool topTabOut = false;
  bool rightTabOut = false;
  bool bottomTabOut = false;
  bool leftTabOut = false;
  
  double randomSeed = 0;
  
  PieceTabConfig() {
    randomSeed = math.Random().nextDouble() * 1000;
  }
}
