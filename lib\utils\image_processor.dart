import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';

class ImageProcessor {
  static final ImagePicker _picker = ImagePicker();
  
  /// Picks an image from gallery or camera
  static Future<XFile?> pickImage({ImageSource source = ImageSource.gallery}) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }
  
  /// Converts XFile to ui.Image
  static Future<ui.Image?> xFileToUiImage(XFile xFile) async {
    try {
      final Uint8List bytes = await xFile.readAsBytes();
      return await bytesToUiImage(bytes);
    } catch (e) {
      print('Error converting XFile to ui.Image: $e');
      return null;
    }
  }
  
  /// Converts bytes to ui.Image
  static Future<ui.Image> bytesToUiImage(Uint8List bytes) async {
    final ui.Codec codec = await ui.instantiateImageCodec(bytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    return frameInfo.image;
  }
  
  /// Processes and resizes image for puzzle use
  static Future<ui.Image> processImageForPuzzle({
    required ui.Image sourceImage,
    required Size targetSize,
  }) async {
    // Convert ui.Image to image package format
    final ByteData? byteData = await sourceImage.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) throw Exception('Failed to convert image to bytes');
    
    final Uint8List bytes = byteData.buffer.asUint8List();
    img.Image? image = img.decodeImage(bytes);
    if (image == null) throw Exception('Failed to decode image');
    
    // Resize image to target size while maintaining aspect ratio
    final double sourceAspectRatio = sourceImage.width / sourceImage.height;
    final double targetAspectRatio = targetSize.width / targetSize.height;
    
    int newWidth, newHeight;
    if (sourceAspectRatio > targetAspectRatio) {
      // Source is wider, fit to width
      newWidth = targetSize.width.toInt();
      newHeight = (targetSize.width / sourceAspectRatio).toInt();
    } else {
      // Source is taller, fit to height
      newHeight = targetSize.height.toInt();
      newWidth = (targetSize.height * sourceAspectRatio).toInt();
    }
    
    // Resize the image
    img.Image resizedImage = img.copyResize(
      image,
      width: newWidth,
      height: newHeight,
      interpolation: img.Interpolation.cubic,
    );
    
    // Create a canvas with the target size and center the resized image
    img.Image finalImage = img.Image(
      width: targetSize.width.toInt(),
      height: targetSize.height.toInt(),
    );
    
    // Fill with a subtle background color
    img.fill(finalImage, color: img.ColorRgb8(240, 240, 240));
    
    // Calculate position to center the image
    final int offsetX = (targetSize.width.toInt() - newWidth) ~/ 2;
    final int offsetY = (targetSize.height.toInt() - newHeight) ~/ 2;
    
    // Composite the resized image onto the final canvas
    img.compositeImage(finalImage, resizedImage, dstX: offsetX, dstY: offsetY);
    
    // Convert back to ui.Image
    final Uint8List finalBytes = Uint8List.fromList(img.encodePng(finalImage));
    return await bytesToUiImage(finalBytes);
  }
  
  /// Creates a sample image for testing
  static Future<ui.Image> createSampleImage(Size size, int imageType) async {
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint();
    
    switch (imageType) {
      case 0:
        _drawNatureScene(canvas, size, paint);
        break;
      case 1:
        _drawCityscape(canvas, size, paint);
        break;
      case 2:
        _drawAbstractArt(canvas, size, paint);
        break;
      default:
        _drawNatureScene(canvas, size, paint);
    }
    
    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(size.width.toInt(), size.height.toInt());
  }
  
  static void _drawNatureScene(Canvas canvas, Size size, Paint paint) {
    // Sky gradient
    paint.shader = ui.Gradient.linear(
      Offset(0, 0),
      Offset(0, size.height * 0.6),
      [
        const Color(0xFF87CEEB),
        const Color(0xFF98FB98),
      ],
    );
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height * 0.6), paint);
    
    // Ground
    paint.shader = null;
    paint.color = const Color(0xFF228B22);
    canvas.drawRect(Rect.fromLTWH(0, size.height * 0.6, size.width, size.height * 0.4), paint);
    
    // Sun
    paint.color = const Color(0xFFFFD700);
    canvas.drawCircle(Offset(size.width * 0.8, size.height * 0.2), 30, paint);
    
    // Mountains
    paint.color = const Color(0xFF696969);
    final mountainPath = Path();
    mountainPath.moveTo(0, size.height * 0.6);
    mountainPath.lineTo(size.width * 0.3, size.height * 0.3);
    mountainPath.lineTo(size.width * 0.6, size.height * 0.4);
    mountainPath.lineTo(size.width, size.height * 0.5);
    mountainPath.lineTo(size.width, size.height * 0.6);
    mountainPath.close();
    canvas.drawPath(mountainPath, paint);
    
    // Trees
    paint.color = const Color(0xFF8B4513);
    canvas.drawRect(Rect.fromLTWH(size.width * 0.2, size.height * 0.5, 10, 40), paint);
    paint.color = const Color(0xFF228B22);
    canvas.drawCircle(Offset(size.width * 0.2 + 5, size.height * 0.5), 20, paint);
  }
  
  static void _drawCityscape(Canvas canvas, Size size, Paint paint) {
    // Night sky gradient
    paint.shader = ui.Gradient.linear(
      Offset(0, 0),
      Offset(0, size.height),
      [
        const Color(0xFF1e3c72),
        const Color(0xFF2a5298),
      ],
    );
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
    
    paint.shader = null;
    
    // Buildings
    final buildingColors = [
      const Color(0xFF2C3E50),
      const Color(0xFF34495E),
      const Color(0xFF3F4F5F),
    ];
    
    for (int i = 0; i < 6; i++) {
      final buildingWidth = size.width / 6;
      final buildingHeight = size.height * (0.3 + (i % 3) * 0.2);
      paint.color = buildingColors[i % buildingColors.length];
      
      canvas.drawRect(
        Rect.fromLTWH(
          i * buildingWidth,
          size.height - buildingHeight,
          buildingWidth - 2,
          buildingHeight,
        ),
        paint,
      );
      
      // Windows
      paint.color = const Color(0xFFFFD700);
      for (int row = 0; row < (buildingHeight / 20).floor(); row++) {
        for (int col = 0; col < 3; col++) {
          canvas.drawRect(
            Rect.fromLTWH(
              i * buildingWidth + 5 + col * 15,
              size.height - buildingHeight + 10 + row * 20,
              8,
              8,
            ),
            paint,
          );
        }
      }
    }
    
    // Moon
    paint.color = const Color(0xFFF5F5DC);
    canvas.drawCircle(Offset(size.width * 0.8, size.height * 0.2), 25, paint);
    
    // Stars
    paint.color = Colors.white;
    for (int i = 0; i < 20; i++) {
      canvas.drawCircle(
        Offset(
          (i * 37) % size.width,
          (i * 23) % (size.height * 0.5),
        ),
        2,
        paint,
      );
    }
  }
  
  static void _drawAbstractArt(Canvas canvas, Size size, Paint paint) {
    // Radial gradient background
    paint.shader = ui.Gradient.radial(
      Offset(size.width / 2, size.height / 2),
      size.width / 2,
      [
        const Color(0xFFFF6B6B),
        const Color(0xFF4ECDC4),
        const Color(0xFF45B7D1),
        const Color(0xFF96CEB4),
      ],
    );
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), paint);
    
    paint.shader = null;
    
    // Geometric shapes
    final colors = [
      Colors.white.withOpacity(0.3),
      Colors.white.withOpacity(0.2),
      Colors.white.withOpacity(0.4),
      Colors.white.withOpacity(0.1),
    ];
    
    for (int i = 0; i < 8; i++) {
      paint.color = colors[i % colors.length];
      
      if (i % 2 == 0) {
        // Circles
        canvas.drawCircle(
          Offset(
            (i * 73) % size.width,
            (i * 97) % size.height,
          ),
          20 + (i * 5),
          paint,
        );
      } else {
        // Rectangles
        canvas.save();
        canvas.translate(
          (i * 83) % size.width,
          (i * 67) % size.height,
        );
        canvas.rotate(i * 0.5);
        canvas.drawRect(
          Rect.fromCenter(center: Offset.zero, width: 40, height: 40),
          paint,
        );
        canvas.restore();
      }
    }
  }
  
  /// Validates if an image is suitable for puzzle creation
  static bool isImageSuitableForPuzzle(ui.Image image) {
    // Check minimum size
    if (image.width < 200 || image.height < 200) {
      return false;
    }
    
    // Check aspect ratio (should be reasonable)
    final aspectRatio = image.width / image.height;
    if (aspectRatio < 0.5 || aspectRatio > 2.0) {
      return false;
    }
    
    return true;
  }
}
