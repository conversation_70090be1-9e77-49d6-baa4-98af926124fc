import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/puzzle_piece.dart';

class ImageSplitter {
  static List<PuzzlePiece> splitImage({
    required Widget originalImage,
    required int rows,
    required int cols,
    required double pieceWidth,
    required double pieceHeight,
  }) {
    List<PuzzlePiece> pieces = [];
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        // Determine tab configuration for realistic puzzle piece shapes
        final hasTopTab = row > 0 && (row + col) % 2 == 0;
        final hasRightTab = col < cols - 1 && (row + col + 1) % 2 == 0;
        final hasBottomTab = row < rows - 1 && (row + col) % 2 == 1;
        final hasLeftTab = col > 0 && (row + col + 1) % 2 == 1;

        final piece = PuzzlePiece(
          id: row * cols + col,
          correctRow: row,
          correctCol: col,
          currentRow: -1, // -1 indicates not placed on board
          currentCol: -1,
          hasTopTab: hasTopTab,
          hasRightTab: hasRightTab,
          hasBottomTab: hasBottomTab,
          hasLeftTab: hasLeftTab,
          imageWidget: _createPieceWidget(
            originalImage: originalImage,
            row: row,
            col: col,
            rows: rows,
            cols: cols,
            pieceWidth: pieceWidth,
            pieceHeight: pieceHeight,
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab,
          ),
        );
        pieces.add(piece);
      }
    }
    
    // Shuffle the pieces
    pieces.shuffle();
    
    return pieces;
  }
  
  static Widget _createPieceWidget({
    required Widget originalImage,
    required int row,
    required int col,
    required int rows,
    required int cols,
    required double pieceWidth,
    required double pieceHeight,
    required bool hasTopTab,
    required bool hasRightTab,
    required bool hasBottomTab,
    required bool hasLeftTab,
  }) {
    return ClipRect(
      child: Container(
        width: pieceWidth,
        height: pieceHeight,
        child: OverflowBox(
          maxWidth: pieceWidth * cols,
          maxHeight: pieceHeight * rows,
          child: Transform.translate(
            offset: Offset(-col * pieceWidth, -row * pieceHeight),
            child: originalImage,
          ),
        ),
      ),
    );
  }
  
  static Widget createSampleImage(int imageIndex) {
    switch (imageIndex) {
      case 0:
        return _createNatureScene();
      case 1:
        return _createCityscape();
      case 2:
        return _createAbstractArt();
      default:
        return _createNatureScene();
    }
  }

  static Widget _createNatureScene() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF87CEEB), // Sky blue
            Color(0xFF98FB98), // Pale green
            Color(0xFF228B22), // Forest green
          ],
          stops: [0.0, 0.6, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // Sun
          Positioned(
            top: 30,
            right: 40,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFFFFD700),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFFFFD700).withValues(alpha: 0.5),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
            ),
          ),
          // Clouds
          Positioned(
            top: 50,
            left: 60,
            child: _buildCloud(40),
          ),
          Positioned(
            top: 80,
            right: 100,
            child: _buildCloud(30),
          ),
          // Mountains
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: CustomPaint(
              size: Size(double.infinity, 120),
              painter: MountainPainter(),
            ),
          ),
          // Trees
          Positioned(
            bottom: 40,
            left: 50,
            child: _buildTree(60),
          ),
          Positioned(
            bottom: 30,
            right: 80,
            child: _buildTree(70),
          ),
          Positioned(
            bottom: 50,
            left: 150,
            child: _buildTree(50),
          ),
          // Flowers
          Positioned(
            bottom: 20,
            left: 30,
            child: _buildFlower(Colors.red),
          ),
          Positioned(
            bottom: 25,
            right: 50,
            child: _buildFlower(Colors.yellow),
          ),
          Positioned(
            bottom: 15,
            left: 120,
            child: _buildFlower(Colors.purple),
          ),
        ],
      ),
    );
  }

  static Widget _createCityscape() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1e3c72),
            Color(0xFF2a5298),
            Color(0xFF4a90e2),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Buildings
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildBuilding(60, 150, Color(0xFF2C3E50)),
                _buildBuilding(80, 200, Color(0xFF34495E)),
                _buildBuilding(50, 120, Color(0xFF2C3E50)),
                _buildBuilding(90, 250, Color(0xFF34495E)),
                _buildBuilding(70, 180, Color(0xFF2C3E50)),
                _buildBuilding(40, 100, Color(0xFF34495E)),
              ],
            ),
          ),
          // Stars
          Positioned(
            top: 30,
            left: 50,
            child: _buildStar(8),
          ),
          Positioned(
            top: 60,
            right: 80,
            child: _buildStar(6),
          ),
          Positioned(
            top: 40,
            left: 150,
            child: _buildStar(10),
          ),
          // Moon
          Positioned(
            top: 40,
            right: 50,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFFF5F5DC),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFFF5F5DC).withValues(alpha: 0.3),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _createAbstractArt() {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          colors: [
            Color(0xFFFF6B6B),
            Color(0xFF4ECDC4),
            Color(0xFF45B7D1),
            Color(0xFF96CEB4),
            Color(0xFFFECA57),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Geometric shapes
          Positioned(
            top: 50,
            left: 40,
            child: Transform.rotate(
              angle: 0.5,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          Positioned(
            top: 120,
            right: 60,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.2),
              ),
            ),
          ),
          Positioned(
            bottom: 80,
            left: 80,
            child: Transform.rotate(
              angle: 1.2,
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.4),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
          // Central mandala-like design
          Center(
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
              ),
              child: CustomPaint(
                painter: MandalaPatternPainter(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildCloud(double size) {
    return Container(
      width: size,
      height: size * 0.6,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            bottom: 0,
            child: Container(
              width: size * 0.4,
              height: size * 0.4,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ),
          Positioned(
            left: size * 0.2,
            bottom: 0,
            child: Container(
              width: size * 0.5,
              height: size * 0.5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              width: size * 0.3,
              height: size * 0.3,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildTree(double height) {
    return Container(
      width: height * 0.4,
      height: height,
      child: Stack(
        children: [
          // Trunk
          Positioned(
            bottom: 0,
            left: height * 0.15,
            child: Container(
              width: height * 0.1,
              height: height * 0.3,
              decoration: BoxDecoration(
                color: Color(0xFF8B4513),
                borderRadius: BorderRadius.circular(height * 0.05),
              ),
            ),
          ),
          // Leaves
          Positioned(
            top: 0,
            left: 0,
            child: Container(
              width: height * 0.4,
              height: height * 0.8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFF228B22),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildFlower(Color color) {
    return Container(
      width: 20,
      height: 20,
      child: Stack(
        children: [
          // Petals
          for (int i = 0; i < 5; i++)
            Positioned.fill(
              child: Transform.rotate(
                angle: i * 2 * 3.14159 / 5,
                child: Container(
                  margin: EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          // Center
          Center(
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.yellow,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildBuilding(double width, double height, Color color) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(5),
          topRight: Radius.circular(5),
        ),
      ),
      child: Column(
        children: [
          SizedBox(height: 10),
          // Windows
          for (int i = 0; i < (height / 30).floor(); i++)
            Padding(
              padding: EdgeInsets.symmetric(vertical: 2, horizontal: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  for (int j = 0; j < (width / 20).floor(); j++)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.yellow.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  static Widget _buildStar(double size) {
    return Container(
      width: size,
      height: size,
      child: CustomPaint(
        painter: StarPainter(),
      ),
    );
  }
}

class MountainPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Color(0xFF696969)
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height);
    path.lineTo(size.width * 0.2, size.height * 0.3);
    path.lineTo(size.width * 0.4, size.height * 0.6);
    path.lineTo(size.width * 0.6, size.height * 0.2);
    path.lineTo(size.width * 0.8, size.height * 0.5);
    path.lineTo(size.width, size.height * 0.4);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class StarPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    final centerX = size.width / 2;
    final centerY = size.height / 2;
    final outerRadius = size.width / 2;
    final innerRadius = outerRadius * 0.4;

    for (int i = 0; i < 10; i++) {
      final angle = i * 3.14159 / 5;
      final radius = i.isEven ? outerRadius : innerRadius;
      final x = centerX + radius * math.cos(angle - 3.14159 / 2);
      final y = centerY + radius * math.sin(angle - 3.14159 / 2);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class MandalaPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw concentric circles
    for (int i = 1; i <= 3; i++) {
      canvas.drawCircle(center, radius * i / 3, paint);
    }

    // Draw radiating lines
    for (int i = 0; i < 8; i++) {
      final angle = i * 3.14159 / 4;
      final startX = center.dx + (radius * 0.3) * math.cos(angle);
      final startY = center.dy + (radius * 0.3) * math.sin(angle);
      final endX = center.dx + radius * math.cos(angle);
      final endY = center.dy + radius * math.sin(angle);

      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Draw grid pattern
    for (double i = 0; i < size.width; i += 40) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i, size.height),
        paint,
      );
    }
    
    for (double i = 0; i < size.height; i += 40) {
      canvas.drawLine(
        Offset(0, i),
        Offset(size.width, i),
        paint,
      );
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
