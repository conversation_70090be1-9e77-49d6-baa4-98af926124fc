import 'package:flutter/material.dart';
import '../models/puzzle_piece.dart';

class ImageSplitter {
  static List<PuzzlePiece> splitImage({
    required Widget originalImage,
    required int rows,
    required int cols,
    required double pieceWidth,
    required double pieceHeight,
  }) {
    List<PuzzlePiece> pieces = [];
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final piece = PuzzlePiece(
          id: row * cols + col,
          correctRow: row,
          correctCol: col,
          currentRow: -1, // -1 indicates not placed on board
          currentCol: -1,
          imageWidget: _createPieceWidget(
            originalImage: originalImage,
            row: row,
            col: col,
            rows: rows,
            cols: cols,
            pieceWidth: pieceWidth,
            pieceHeight: pieceHeight,
          ),
        );
        pieces.add(piece);
      }
    }
    
    // Shuffle the pieces
    pieces.shuffle();
    
    return pieces;
  }
  
  static Widget _createPieceWidget({
    required Widget originalImage,
    required int row,
    required int col,
    required int rows,
    required int cols,
    required double pieceWidth,
    required double pieceHeight,
  }) {
    return ClipRect(
      child: Container(
        width: pieceWidth,
        height: pieceHeight,
        child: OverflowBox(
          maxWidth: pieceWidth * cols,
          maxHeight: pieceHeight * rows,
          child: Transform.translate(
            offset: Offset(-col * pieceWidth, -row * pieceHeight),
            child: originalImage,
          ),
        ),
      ),
    );
  }
  
  static Widget createSampleImage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade300,
            Colors.purple.shade300,
            Colors.pink.shade300,
            Colors.orange.shade300,
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: CustomPaint(
              painter: PatternPainter(),
            ),
          ),
          // Central design
          Center(
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                Icons.star,
                size: 60,
                color: Colors.amber.shade600,
              ),
            ),
          ),
          // Corner decorations
          Positioned(
            top: 20,
            left: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.shade400,
              ),
            ),
          ),
          Positioned(
            top: 20,
            right: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.green.shade400,
              ),
            ),
          ),
          Positioned(
            bottom: 20,
            left: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.blue.shade400,
              ),
            ),
          ),
          Positioned(
            bottom: 20,
            right: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.yellow.shade400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Draw grid pattern
    for (double i = 0; i < size.width; i += 40) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i, size.height),
        paint,
      );
    }
    
    for (double i = 0; i < size.height; i += 40) {
      canvas.drawLine(
        Offset(0, i),
        Offset(size.width, i),
        paint,
      );
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
