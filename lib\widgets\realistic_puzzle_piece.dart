import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../utils/realistic_puzzle_generator.dart';

class RealisticPuzzlePiece extends StatefulWidget {
  final ui.Image sourceImage;
  final Size pieceSize;
  final Offset imageOffset;
  final PieceTabConfig tabConfig;
  final int row;
  final int col;
  final bool isPlaced;
  final bool isSelected;
  final bool isHinted;
  final VoidCallback? onTap;
  final Function(Offset)? onDragStart;
  final Function(Offset)? onDragUpdate;
  final Function()? onDragEnd;
  
  const RealisticPuzzlePiece({
    super.key,
    required this.sourceImage,
    required this.pieceSize,
    required this.imageOffset,
    required this.tabConfig,
    required this.row,
    required this.col,
    this.isPlaced = false,
    this.isSelected = false,
    this.isHinted = false,
    this.onTap,
    this.onDragStart,
    this.onDragUpdate,
    this.onDragEnd,
  });
  
  @override
  State<RealisticPuzzlePiece> createState() => _RealisticPuzzlePieceState();
}

class _RealisticPuzzlePieceState extends State<RealisticPuzzlePiece>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  void didUpdateWidget(RealisticPuzzlePiece oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onPanStart: (details) {
        widget.onDragStart?.call(details.localPosition);
        _animationController.forward();
      },
      onPanUpdate: (details) {
        widget.onDragUpdate?.call(details.localPosition);
      },
      onPanEnd: (details) {
        widget.onDragEnd?.call();
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.pieceSize.width,
              height: widget.pieceSize.height,
              child: CustomPaint(
                painter: RealisticPuzzlePiecePainter(
                  sourceImage: widget.sourceImage,
                  imageOffset: widget.imageOffset,
                  tabConfig: widget.tabConfig,
                  isSelected: widget.isSelected,
                  isHinted: widget.isHinted,
                  elevation: _elevationAnimation.value,
                ),
                size: widget.pieceSize,
              ),
            ),
          );
        },
      ),
    );
  }
}

class RealisticPuzzlePiecePainter extends CustomPainter {
  final ui.Image sourceImage;
  final Offset imageOffset;
  final PieceTabConfig tabConfig;
  final bool isSelected;
  final bool isHinted;
  final double elevation;
  
  RealisticPuzzlePiecePainter({
    required this.sourceImage,
    required this.imageOffset,
    required this.tabConfig,
    this.isSelected = false,
    this.isHinted = false,
    this.elevation = 2.0,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // Generate the piece path
    final piecePath = RealisticPuzzleGenerator.generatePiecePath(
      pieceSize: size,
      hasTopTab: tabConfig.hasTopTab,
      hasRightTab: tabConfig.hasRightTab,
      hasBottomTab: tabConfig.hasBottomTab,
      hasLeftTab: tabConfig.hasLeftTab,
      topTabOut: tabConfig.topTabOut,
      rightTabOut: tabConfig.rightTabOut,
      bottomTabOut: tabConfig.bottomTabOut,
      leftTabOut: tabConfig.leftTabOut,
      randomSeed: tabConfig.randomSeed,
    );
    
    // Draw shadow for 3D effect
    _drawShadow(canvas, piecePath, elevation);
    
    // Clip and draw the image
    canvas.save();
    canvas.clipPath(piecePath);
    
    // Draw the image portion
    final paint = Paint()
      ..filterQuality = FilterQuality.high;
    
    final srcRect = Rect.fromLTWH(
      imageOffset.dx,
      imageOffset.dy,
      size.width,
      size.height,
    );
    
    final dstRect = Rect.fromLTWH(0, 0, size.width, size.height);
    
    canvas.drawImageRect(sourceImage, srcRect, dstRect, paint);
    canvas.restore();
    
    // Draw piece border with 3D effect
    _drawPieceBorder(canvas, piecePath, size);
    
    // Draw selection or hint effects
    if (isSelected || isHinted) {
      _drawSelectionEffect(canvas, piecePath);
    }
  }
  
  void _drawShadow(Canvas canvas, Path piecePath, double elevation) {
    final shadowPath = piecePath.shift(Offset(elevation * 0.5, elevation * 0.5));
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, elevation);
    
    canvas.drawPath(shadowPath, shadowPaint);
  }
  
  void _drawPieceBorder(Canvas canvas, Path piecePath, Size size) {
    // Outer border (darker)
    final outerBorderPaint = Paint()
      ..color = Colors.black.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    
    canvas.drawPath(piecePath, outerBorderPaint);
    
    // Inner highlight (lighter)
    final innerHighlightPaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;
    
    // Create a slightly inset path for the highlight
    final insetPath = _createInsetPath(piecePath, -1.0);
    canvas.drawPath(insetPath, innerHighlightPaint);
  }
  
  void _drawSelectionEffect(Canvas canvas, Path piecePath) {
    final effectColor = isHinted ? Colors.yellow : Colors.blue;
    
    // Glowing effect
    final glowPaint = Paint()
      ..color = effectColor.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);
    
    canvas.drawPath(piecePath, glowPaint);
    
    // Bright border
    final borderPaint = Paint()
      ..color = effectColor.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;
    
    canvas.drawPath(piecePath, borderPaint);
  }
  
  Path _createInsetPath(Path originalPath, double inset) {
    // This is a simplified inset - in a real implementation,
    // you might want to use a more sophisticated path offsetting algorithm
    final bounds = originalPath.getBounds();
    final matrix = Matrix4.identity();
    matrix.translate(bounds.center.dx, bounds.center.dy);
    matrix.scale(1.0 + inset / bounds.width, 1.0 + inset / bounds.height);
    matrix.translate(-bounds.center.dx, -bounds.center.dy);
    
    return originalPath.transform(matrix.storage);
  }
  
  @override
  bool shouldRepaint(covariant RealisticPuzzlePiecePainter oldDelegate) {
    return oldDelegate.isSelected != isSelected ||
           oldDelegate.isHinted != isHinted ||
           oldDelegate.elevation != elevation;
  }
}
