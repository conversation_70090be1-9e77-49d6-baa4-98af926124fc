import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../utils/realistic_puzzle_generator.dart';

class RealisticPuzzlePieceModel {
  final int id;
  final int correctRow;
  final int correctCol;
  final PieceTabConfig tabConfig;
  final Offset imageOffset;
  final Size pieceSize;
  
  Offset currentPosition;
  bool isPlaced;
  bool isSelected;
  bool isHinted;
  int? currentBoardRow;
  int? currentBoardCol;
  
  RealisticPuzzlePieceModel({
    required this.id,
    required this.correctRow,
    required this.correctCol,
    required this.tabConfig,
    required this.imageOffset,
    required this.pieceSize,
    required this.currentPosition,
    this.isPlaced = false,
    this.isSelected = false,
    this.isHinted = false,
    this.currentBoardRow,
    this.currentBoardCol,
  });
  
  bool get isInCorrectPosition => 
      isPlaced && 
      currentBoardRow == correctRow && 
      currentBoardCol == correctCol;
  
  RealisticPuzzlePieceModel copyWith({
    Offset? currentPosition,
    bool? isPlaced,
    bool? isSelected,
    bool? isHinted,
    int? currentBoardRow,
    int? currentBoardCol,
  }) {
    return RealisticPuzzlePieceModel(
      id: id,
      correctRow: correctRow,
      correctCol: correctCol,
      tabConfig: tabConfig,
      imageOffset: imageOffset,
      pieceSize: pieceSize,
      currentPosition: currentPosition ?? this.currentPosition,
      isPlaced: isPlaced ?? this.isPlaced,
      isSelected: isSelected ?? this.isSelected,
      isHinted: isHinted ?? this.isHinted,
      currentBoardRow: currentBoardRow ?? this.currentBoardRow,
      currentBoardCol: currentBoardCol ?? this.currentBoardCol,
    );
  }
  
  /// Calculates the distance from current position to correct position
  double distanceToCorrectPosition(Size boardPieceSize) {
    final correctPosition = Offset(
      correctCol * boardPieceSize.width,
      correctRow * boardPieceSize.height,
    );
    return (currentPosition - correctPosition).distance;
  }
  
  /// Checks if this piece can connect to another piece
  bool canConnectTo(RealisticPuzzlePieceModel other, ConnectionDirection direction) {
    switch (direction) {
      case ConnectionDirection.right:
        if (!tabConfig.hasRightTab || !other.tabConfig.hasLeftTab) return false;
        return tabConfig.rightTabOut != other.tabConfig.leftTabOut;
        
      case ConnectionDirection.bottom:
        if (!tabConfig.hasBottomTab || !other.tabConfig.hasTopTab) return false;
        return tabConfig.bottomTabOut != other.tabConfig.topTabOut;
        
      case ConnectionDirection.left:
        if (!tabConfig.hasLeftTab || !other.tabConfig.hasRightTab) return false;
        return tabConfig.leftTabOut != other.tabConfig.rightTabOut;
        
      case ConnectionDirection.top:
        if (!tabConfig.hasTopTab || !other.tabConfig.hasBottomTab) return false;
        return tabConfig.topTabOut != other.tabConfig.bottomTabOut;
    }
  }
  
  /// Gets the expected position of a neighboring piece
  Offset getNeighborPosition(ConnectionDirection direction, Size pieceSize) {
    switch (direction) {
      case ConnectionDirection.right:
        return Offset(currentPosition.dx + pieceSize.width, currentPosition.dy);
      case ConnectionDirection.bottom:
        return Offset(currentPosition.dx, currentPosition.dy + pieceSize.height);
      case ConnectionDirection.left:
        return Offset(currentPosition.dx - pieceSize.width, currentPosition.dy);
      case ConnectionDirection.top:
        return Offset(currentPosition.dx, currentPosition.dy - pieceSize.height);
    }
  }
}

class RealisticPuzzleGameModel extends ChangeNotifier {
  final ui.Image sourceImage;
  final int rows;
  final int cols;
  final Size pieceSize;
  
  late List<List<RealisticPuzzlePieceModel?>> board;
  late List<RealisticPuzzlePieceModel> availablePieces;
  late List<RealisticPuzzlePieceModel> allPieces;
  
  int moves = 0;
  DateTime? startTime;
  DateTime? endTime;
  bool isCompleted = false;
  bool usedHints = false;
  int hintsUsed = 0;
  List<int> hintedPieceIds = [];
  
  RealisticPuzzlePieceModel? selectedPiece;
  
  RealisticPuzzleGameModel({
    required this.sourceImage,
    required this.rows,
    required this.cols,
    required this.pieceSize,
  }) {
    _initializeGame();
  }
  
  void _initializeGame() {
    // Initialize board
    board = List.generate(rows, (row) => 
        List.generate(cols, (col) => null));
    
    // Generate tab configuration
    final tabConfigs = RealisticPuzzleGenerator.generateTabConfiguration(rows, cols);
    
    // Create pieces
    allPieces = [];
    availablePieces = [];
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final piece = RealisticPuzzlePieceModel(
          id: row * cols + col,
          correctRow: row,
          correctCol: col,
          tabConfig: tabConfigs[row][col],
          imageOffset: Offset(
            col * pieceSize.width,
            row * pieceSize.height,
          ),
          pieceSize: pieceSize,
          currentPosition: Offset(
            col * pieceSize.width,
            row * pieceSize.height,
          ),
        );
        
        allPieces.add(piece);
        availablePieces.add(piece);
      }
    }
    
    // Shuffle available pieces
    availablePieces.shuffle();
  }
  
  void startGame() {
    startTime = DateTime.now();
    moves = 0;
    isCompleted = false;
    usedHints = false;
    hintsUsed = 0;
    hintedPieceIds.clear();
    notifyListeners();
  }
  
  bool placePiece(RealisticPuzzlePieceModel piece, int boardRow, int boardCol) {
    // Check if position is valid
    if (boardRow < 0 || boardRow >= rows || boardCol < 0 || boardCol >= cols) {
      return false;
    }
    
    // Check if position is already occupied
    if (board[boardRow][boardCol] != null) {
      return false;
    }
    
    // Remove piece from current position if it's on the board
    if (piece.isPlaced && piece.currentBoardRow != null && piece.currentBoardCol != null) {
      board[piece.currentBoardRow!][piece.currentBoardCol!] = null;
    } else {
      availablePieces.remove(piece);
    }
    
    // Place piece in new position
    board[boardRow][boardCol] = piece.copyWith(
      isPlaced: true,
      currentBoardRow: boardRow,
      currentBoardCol: boardCol,
      currentPosition: Offset(
        boardCol * pieceSize.width,
        boardRow * pieceSize.height,
      ),
    );
    
    moves++;
    
    // Check if puzzle is completed
    _checkCompletion();
    
    notifyListeners();
    return true;
  }
  
  void removePieceFromBoard(RealisticPuzzlePieceModel piece) {
    if (piece.isPlaced && piece.currentBoardRow != null && piece.currentBoardCol != null) {
      board[piece.currentBoardRow!][piece.currentBoardCol!] = null;
      availablePieces.add(piece.copyWith(
        isPlaced: false,
        currentBoardRow: null,
        currentBoardCol: null,
      ));
      notifyListeners();
    }
  }
  
  void _checkCompletion() {
    bool allPlaced = true;
    bool allCorrect = true;
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final piece = board[row][col];
        if (piece == null) {
          allPlaced = false;
          allCorrect = false;
          break;
        }
        if (!piece.isInCorrectPosition) {
          allCorrect = false;
        }
      }
      if (!allPlaced) break;
    }
    
    if (allPlaced && allCorrect) {
      isCompleted = true;
      endTime = DateTime.now();
    }
  }
  
  void useHint() {
    if (hintsUsed >= 3) return;
    
    // Find a piece that can be hinted
    for (var piece in availablePieces) {
      if (!hintedPieceIds.contains(piece.id) && 
          board[piece.correctRow][piece.correctCol] == null) {
        hintedPieceIds.add(piece.id);
        usedHints = true;
        hintsUsed++;
        notifyListeners();
        break;
      }
    }
  }
  
  bool isPieceHinted(int pieceId) {
    return hintedPieceIds.contains(pieceId);
  }
  
  int get remainingHints => 3 - hintsUsed;
  
  Duration? get gameDuration {
    if (startTime == null) return null;
    final end = endTime ?? DateTime.now();
    return end.difference(startTime!);
  }
  
  void resetGame() {
    // Clear board
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        board[row][col] = null;
      }
    }
    
    // Reset all pieces
    availablePieces.clear();
    for (var piece in allPieces) {
      availablePieces.add(piece.copyWith(
        isPlaced: false,
        currentBoardRow: null,
        currentBoardCol: null,
        isSelected: false,
        isHinted: false,
      ));
    }
    
    // Shuffle pieces
    availablePieces.shuffle();
    
    // Reset game state
    moves = 0;
    isCompleted = false;
    startTime = null;
    endTime = null;
    usedHints = false;
    hintsUsed = 0;
    hintedPieceIds.clear();
    selectedPiece = null;
    
    notifyListeners();
  }
}

enum ConnectionDirection {
  top,
  right,
  bottom,
  left,
}
