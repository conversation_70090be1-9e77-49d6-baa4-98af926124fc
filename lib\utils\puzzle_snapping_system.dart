import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/realistic_puzzle_piece_model.dart';

class PuzzleSnappingSystem {
  static const double snapDistance = 30.0; // Distance threshold for snapping
  static const double snapTolerance = 15.0; // Tolerance for piece alignment
  static const Duration snapAnimationDuration = Duration(milliseconds: 200);
  
  /// Checks if a piece can snap to a position on the board
  static SnapResult checkSnapping({
    required RealisticPuzzlePieceModel draggedPiece,
    required Offset dropPosition,
    required List<List<RealisticPuzzlePieceModel?>> board,
    required Size pieceSize,
    required int gridRows,
    required int gridCols,
  }) {
    // Convert drop position to grid coordinates
    final gridCol = (dropPosition.dx / pieceSize.width).round();
    final gridRow = (dropPosition.dy / pieceSize.height).round();
    
    // Check if position is within bounds
    if (gridRow < 0 || gridRow >= gridRows || gridCol < 0 || gridCol >= gridCols) {
      return SnapResult.noSnap();
    }
    
    // Check if the position is the correct one for this piece
    if (gridRow != draggedPiece.correctRow || gridCol != draggedPiece.correctCol) {
      return SnapResult.noSnap();
    }
    
    // Check if position is already occupied
    if (board[gridRow][gridCol] != null) {
      return SnapResult.noSnap();
    }
    
    // Calculate exact snap position
    final snapPosition = Offset(
      gridCol * pieceSize.width,
      gridRow * pieceSize.height,
    );
    
    // Check distance threshold
    final distance = (dropPosition - snapPosition).distance;
    if (distance > snapDistance) {
      return SnapResult.noSnap();
    }
    
    // Check if piece fits with adjacent pieces (shape compatibility)
    final shapeCompatible = _checkShapeCompatibility(
      draggedPiece,
      gridRow,
      gridCol,
      board,
    );
    
    if (!shapeCompatible) {
      return SnapResult.noSnap();
    }
    
    return SnapResult.snap(
      position: snapPosition,
      gridRow: gridRow,
      gridCol: gridCol,
      confidence: 1.0 - (distance / snapDistance),
    );
  }
  
  /// Checks if the piece shape is compatible with adjacent pieces
  static bool _checkShapeCompatibility(
    RealisticPuzzlePieceModel piece,
    int gridRow,
    int gridCol,
    List<List<RealisticPuzzlePieceModel?>> board,
  ) {
    // Check top neighbor
    if (gridRow > 0) {
      final topPiece = board[gridRow - 1][gridCol];
      if (topPiece != null) {
        // The top piece's bottom tab should be opposite of current piece's top tab
        if (piece.tabConfig.hasTopTab && topPiece.tabConfig.hasBottomTab) {
          if (piece.tabConfig.topTabOut == topPiece.tabConfig.bottomTabOut) {
            return false; // Both tabs going in same direction - incompatible
          }
        }
      }
    }
    
    // Check right neighbor
    if (gridCol < board[0].length - 1) {
      final rightPiece = board[gridRow][gridCol + 1];
      if (rightPiece != null) {
        if (piece.tabConfig.hasRightTab && rightPiece.tabConfig.hasLeftTab) {
          if (piece.tabConfig.rightTabOut == rightPiece.tabConfig.leftTabOut) {
            return false;
          }
        }
      }
    }
    
    // Check bottom neighbor
    if (gridRow < board.length - 1) {
      final bottomPiece = board[gridRow + 1][gridCol];
      if (bottomPiece != null) {
        if (piece.tabConfig.hasBottomTab && bottomPiece.tabConfig.hasTopTab) {
          if (piece.tabConfig.bottomTabOut == bottomPiece.tabConfig.topTabOut) {
            return false;
          }
        }
      }
    }
    
    // Check left neighbor
    if (gridCol > 0) {
      final leftPiece = board[gridRow][gridCol - 1];
      if (leftPiece != null) {
        if (piece.tabConfig.hasLeftTab && leftPiece.tabConfig.hasRightTab) {
          if (piece.tabConfig.leftTabOut == leftPiece.tabConfig.rightTabOut) {
            return false;
          }
        }
      }
    }
    
    return true;
  }
  
  /// Finds the best snap position for a piece among multiple candidates
  static SnapResult findBestSnapPosition({
    required RealisticPuzzlePieceModel draggedPiece,
    required Offset dropPosition,
    required List<List<RealisticPuzzlePieceModel?>> board,
    required Size pieceSize,
    required int gridRows,
    required int gridCols,
  }) {
    final candidates = <SnapResult>[];
    
    // Check a 3x3 grid around the drop position
    final centerCol = (dropPosition.dx / pieceSize.width).round();
    final centerRow = (dropPosition.dy / pieceSize.height).round();
    
    for (int row = centerRow - 1; row <= centerRow + 1; row++) {
      for (int col = centerCol - 1; col <= centerCol + 1; col++) {
        if (row >= 0 && row < gridRows && col >= 0 && col < gridCols) {
          final candidatePosition = Offset(
            col * pieceSize.width,
            row * pieceSize.height,
          );
          
          final result = checkSnapping(
            draggedPiece: draggedPiece,
            dropPosition: candidatePosition,
            board: board,
            pieceSize: pieceSize,
            gridRows: gridRows,
            gridCols: gridCols,
          );
          
          if (result.canSnap) {
            candidates.add(result);
          }
        }
      }
    }
    
    if (candidates.isEmpty) {
      return SnapResult.noSnap();
    }
    
    // Return the candidate with highest confidence (closest distance)
    candidates.sort((a, b) => b.confidence.compareTo(a.confidence));
    return candidates.first;
  }
  
  /// Animates a piece to its snap position
  static void animateToSnapPosition({
    required RealisticPuzzlePieceModel piece,
    required Offset targetPosition,
    required TickerProvider vsync,
    required VoidCallback onComplete,
  }) {
    final controller = AnimationController(
      duration: snapAnimationDuration,
      vsync: vsync,
    );
    
    final startPosition = piece.currentPosition;
    final animation = Tween<Offset>(
      begin: startPosition,
      end: targetPosition,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeOutBack,
    ));
    
    animation.addListener(() {
      piece.currentPosition = animation.value;
    });
    
    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        controller.dispose();
        onComplete();
      }
    });
    
    controller.forward();
  }
  
  /// Checks if two pieces can connect based on their tab configuration
  static bool canPiecesConnect(
    RealisticPuzzlePieceModel piece1,
    RealisticPuzzlePieceModel piece2,
    ConnectionDirection direction,
  ) {
    switch (direction) {
      case ConnectionDirection.horizontal:
        // piece1 is left, piece2 is right
        if (!piece1.tabConfig.hasRightTab || !piece2.tabConfig.hasLeftTab) {
          return false;
        }
        return piece1.tabConfig.rightTabOut != piece2.tabConfig.leftTabOut;
        
      case ConnectionDirection.vertical:
        // piece1 is top, piece2 is bottom
        if (!piece1.tabConfig.hasBottomTab || !piece2.tabConfig.hasTopTab) {
          return false;
        }
        return piece1.tabConfig.bottomTabOut != piece2.tabConfig.topTabOut;
    }
  }
}

class SnapResult {
  final bool canSnap;
  final Offset position;
  final int gridRow;
  final int gridCol;
  final double confidence;
  
  SnapResult._({
    required this.canSnap,
    required this.position,
    required this.gridRow,
    required this.gridCol,
    required this.confidence,
  });
  
  factory SnapResult.snap({
    required Offset position,
    required int gridRow,
    required int gridCol,
    required double confidence,
  }) {
    return SnapResult._(
      canSnap: true,
      position: position,
      gridRow: gridRow,
      gridCol: gridCol,
      confidence: confidence,
    );
  }
  
  factory SnapResult.noSnap() {
    return SnapResult._(
      canSnap: false,
      position: Offset.zero,
      gridRow: -1,
      gridCol: -1,
      confidence: 0.0,
    );
  }
}

enum ConnectionDirection {
  horizontal,
  vertical,
}
