import 'package:flutter/material.dart';
import 'puzzle_piece.dart';

enum PuzzleDifficulty { easy, medium, hard }

class PuzzleGame extends ChangeNotifier {
  final PuzzleDifficulty difficulty;
  final List<List<PuzzlePiece?>> board;
  final List<PuzzlePiece> availablePieces;
  final int rows;
  final int cols;
  int moves;
  DateTime? startTime;
  DateTime? endTime;
  bool isCompleted;
  
  PuzzleGame({
    required this.difficulty,
    required this.rows,
    required this.cols,
  }) : board = List.generate(
         rows, 
         (index) => List.generate(cols, (index) => null)
       ),
       availablePieces = [],
       moves = 0,
       isCompleted = false;
  
  static int getGridSize(PuzzleDifficulty difficulty) {
    switch (difficulty) {
      case PuzzleDifficulty.easy:
        return 3;
      case PuzzleDifficulty.medium:
        return 4;
      case PuzzleDifficulty.hard:
        return 5;
    }
  }
  
  void startGame() {
    startTime = DateTime.now();
    moves = 0;
    isCompleted = false;
    notifyListeners();
  }
  
  void placePiece(Puzzle<PERSON>iece piece, int row, int col) {
    // Remove piece from current position if it's on the board
    if (piece.isPlaced) {
      board[piece.currentRow][piece.currentCol] = null;
    } else {
      availablePieces.remove(piece);
    }
    
    // Place piece in new position
    board[row][col] = piece.copyWith(
      currentRow: row,
      currentCol: col,
      isPlaced: true,
    );
    
    moves++;
    
    // Check if puzzle is completed
    _checkCompletion();
    
    notifyListeners();
  }
  
  void removePieceFromBoard(PuzzlePiece piece) {
    if (piece.isPlaced) {
      board[piece.currentRow][piece.currentCol] = null;
      availablePieces.add(piece.copyWith(isPlaced: false));
      notifyListeners();
    }
  }
  
  void _checkCompletion() {
    bool allPlaced = true;
    bool allCorrect = true;
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final piece = board[row][col];
        if (piece == null) {
          allPlaced = false;
          allCorrect = false;
          break;
        }
        if (!piece.isInCorrectPosition) {
          allCorrect = false;
        }
      }
      if (!allPlaced) break;
    }
    
    if (allPlaced && allCorrect) {
      isCompleted = true;
      endTime = DateTime.now();
    }
  }
  
  Duration? get gameDuration {
    if (startTime == null) return null;
    final end = endTime ?? DateTime.now();
    return end.difference(startTime!);
  }
  
  void resetGame() {
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        board[row][col] = null;
      }
    }
    
    // Move all pieces back to available pieces
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final piece = board[row][col];
        if (piece != null) {
          availablePieces.add(piece.copyWith(isPlaced: false));
        }
      }
    }
    
    // Shuffle available pieces
    availablePieces.shuffle();
    
    moves = 0;
    isCompleted = false;
    startTime = null;
    endTime = null;
    
    notifyListeners();
  }
}
